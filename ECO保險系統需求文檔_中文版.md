# ECO保險系統需求文檔

## 版本信息
- **文檔版本**: 1.0
- **創建日期**: 2025-07-08
- **系統名稱**: ECO保險系統 (Employee Compensation Online System)
- **開發框架**: ASP.NET MVC 5 + Entity Framework 6
- **數據庫**: SQL Server

## 1. 系統概述

### 1.1 業務背景
ECO保險系統是一個專為香港建築行業設計的員工工傷補償管理系統。系統主要服務於建築公司、保險公司和相關監管機構，用於處理工傷事故報告、補償申請和相關文檔管理。

### 1.2 系統目標
- 數字化工傷事故報告流程
- 標準化補償申請處理
- 提供多語言支持（中文/英文）
- 確保數據安全和合規性
- 提高處理效率和準確性

### 1.3 系統範圍
- 用戶管理和權限控制
- 工傷申請表格管理（表格2和表格2B）
- 工傷報表生成和管理
- 部門和職位管理
- 文檔生成和郵件通知
- 操作日誌和審計跟蹤

## 2. 功能需求

### 2.1 用戶管理模塊

#### 2.1.1 用戶註冊和認證
- **功能描述**: 支持用戶註冊、登錄和密碼管理
- **主要功能**:
  - 用戶註冊（需要管理員審核）
  - Windows域認證集成
  - 密碼加密存儲（MD5）
  - 密碼修改功能
  - 會話管理

#### 2.1.2 用戶權限管理
- **角色類型**:
  - 管理員：完整系統訪問權限
  - 普通用戶：受限訪問權限
- **權限級別**:
  - 沒有修改權限
  - 修改自己的表格
  - 修改全部表格

#### 2.1.3 用戶信息管理
- **用戶屬性**:
  - 基本信息：賬號、姓名、英文姓名、手機號、郵箱
  - 組織信息：部門ID（支持多級部門）
  - 權限信息：角色、權限級別
  - 審計信息：創建時間、最後登錄時間

### 2.2 申請管理模塊

#### 2.2.1 表格2（工傷事故報告）
- **功能描述**: 處理工傷事故的初始報告
- **主要字段**:
  - 申請人信息：姓名、職位、部門
  - 事故詳情：發生時間、地點、原因、結果
  - 傷者信息：身份證類型、號碼、聯繫方式
  - 保險詳情：保險號碼、保險公司信息
  - 收入詳情：工作天數、其他收入

#### 2.2.2 表格2B（工傷補償申請）
- **功能描述**: 處理工傷補償的正式申請
- **主要字段**:
  - 基本信息：申請人、受傷員工信息
  - 事故信息：事故類型、發生地點、受傷部位
  - 醫療信息：醫院名稱、治療詳情
  - 補償信息：申請金額、支付狀態
  - 語言設置：支持中英文雙語

#### 2.2.3 申請流程管理
- **狀態跟蹤**: 草稿、已提交、審核中、已批准、已拒絕
- **審批流程**: 多級審批機制
- **通知機制**: 郵件通知相關人員
- **文檔生成**: 自動生成PDF文檔

### 2.3 報表管理模塊

#### 2.3.1 工傷報表生成
- **功能描述**: 生成各類工傷統計報表
- **報表類型**:
  - 月度工傷統計報表
  - 部門工傷分析報表
  - 事故類型統計報表
  - 補償金額統計報表

#### 2.3.2 報表管理功能
- **版本控制**: 支持報表版本管理
- **文件附件**: 支持附件上傳和管理
- **導出功能**: PDF格式導出
- **權限控制**: 基於部門的訪問控制

### 2.4 部門管理模塊

#### 2.4.1 部門結構管理
- **功能描述**: 管理組織架構和部門信息
- **部門類型**: 支持多級部門結構
- **部門信息**: 部門名稱、語言設置、父級部門
- **部門列表**: 包含大量建築項目部門

#### 2.4.2 職位管理
- **功能描述**: 管理職位信息和類型
- **職位屬性**: 職位名稱、類型、語言設置
- **職位分類**: 按工程類型分類

### 2.5 系統管理模塊

#### 2.5.1 屬性管理
- **功能描述**: 管理系統中的各類屬性和選項
- **屬性類型**:
  - 工程性質
  - 公營工程
  - 墜下地點細節
  - 種族分類
  - 語言能力等級

#### 2.5.2 日誌管理
- **功能描述**: 記錄和查詢系統操作日誌
- **日誌類型**:
  - 表格2編輯
  - 表格2B編輯
  - 用戶登錄
  - 系統操作
- **日誌內容**: 操作人、操作時間、操作內容、相關對象

## 3. 非功能性需求

### 3.1 性能需求
- **響應時間**: 頁面加載時間不超過3秒
- **併發用戶**: 支持100個併發用戶
- **數據庫連接**: 連接超時時間400秒
- **文件處理**: 支持大文件上傳和PDF生成

### 3.2 安全需求
- **身份認證**: Windows域認證 + 表單認證
- **密碼安全**: MD5加密存儲
- **會話管理**: 14天令牌有效期
- **權限控制**: 基於角色和權限的訪問控制
- **數據保護**: 敏感數據加密存儲

### 3.3 可用性需求
- **系統可用性**: 99.5%以上
- **多語言支持**: 中文（繁體）和英文
- **瀏覽器兼容**: 支持主流瀏覽器
- **移動設備**: 響應式設計支持

### 3.4 可維護性需求
- **代碼結構**: 分層架構設計
- **日誌記錄**: 完整的操作日誌
- **錯誤處理**: 統一的異常處理機制
- **文檔管理**: 完整的技術文檔

## 4. 數據模型

### 4.1 核心實體

#### 4.1.1 用戶實體 (T_USER)
```
- ID: int (主鍵)
- Account: nvarchar(20) (賬號)
- Password: varchar(100) (密碼)
- Name: nvarchar(100) (姓名)
- EnglishName: varchar(100) (英文姓名)
- Mobile: varchar(100) (手機號)
- Email: varchar(100) (郵箱)
- DepartMentID: int (部門ID)
- DepartMentID1: int (部門ID1)
- DepartMentID2: int (部門ID2)
- DepartMentID3: int (部門ID3)
- Role: tinyint (角色)
- Authortity: tinyint (權限)
- CreateTime: datetime (創建時間)
```

#### 4.1.2 申請實體 (T_APPLY_2)
```
- ID: bigint (主鍵)
- JobID: int (職位ID)
- UserID: int (用戶ID)
- ApplyerName: nvarchar(50) (申請人姓名)
- EmployeeName: nvarchar(50) (員工姓名)
- EmployeeIDType: tinyint (身份證類型)
- EmployeeID: varchar(50) (身份證號)
- HappenedDate: datetime (事故發生時間)
- AccidentResult: tinyint (事故結果)
- HappenedAddress: nvarchar(200) (事故地點)
- HospitalName: nvarchar(100) (醫院名稱)
- InsuranceDetail: nvarchar(500) (保險詳情)
- InsuranceNumber: varchar(50) (保險號碼)
- CreateTime: datetime (創建時間)
- Language: tinyint (語言)
```

#### 4.1.3 申請實體2B (T_APPLY_2B)
```
- ID: bigint (主鍵)
- JobID: int (職位ID)
- UserID: int (用戶ID)
- ApplyerName: nvarchar(50) (申請人姓名)
- EmployeeName: nvarchar(50) (員工姓名)
- EmployeeIDType: tinyint (身份證類型)
- EmployeeID: varchar(50) (身份證號)
- HappenedDate: datetime (事故發生時間)
- AccidentResult: tinyint (事故結果)
- HappenedAddress: nvarchar(200) (事故地點)
- HospitalName: nvarchar(100) (醫院名稱)
- CompensationAmount: decimal (補償金額)
- HasPaid: bit (是否已支付)
- PayDate: datetime (支付日期)
- CreateTime: datetime (創建時間)
- Language: tinyint (語言)
```

#### 4.1.4 部門實體 (T_DEPARTMENT)
```
- ID: int (主鍵)
- Name: nvarchar(100) (部門名稱)
- Language: tinyint (語言)
- ParentID: int (父級部門ID)
- CreateTime: datetime (創建時間)
```

#### 4.1.5 職位實體 (T_JOB)
```
- ID: int (主鍵)
- Name: nvarchar(100) (職位名稱)
- Type: tinyint (職位類型)
- Language: tinyint (語言)
- CreateTime: datetime (創建時間)
```

#### 4.1.6 報表實體 (T_Report_O)
```
- ID: bigint (主鍵)
- ApplyName: nvarchar(50) (申請人姓名)
- DeptID: int (部門ID)
- VersionID: bigint (版本ID)
- VersionControl: varchar(50) (版本控制)
- AttachUrl: nvarchar(500) (附件URL)
- CreateTime: datetime (創建時間)
- Language: tinyint (語言)
```

#### 4.1.7 屬性實體 (T_ATTRIBUTE)
```
- ID: int (主鍵)
- Name: nvarchar(100) (屬性名稱)
- GroupName: nvarchar(20) (分組名稱)
- Type: tinyint (屬性類型)
- Language: tinyint (語言)
- SpecialIndex: varchar(3) (特殊索引)
- CreateTime: datetime (創建時間)
```

#### 4.1.8 日誌實體 (T_LOG)
```
- ID: bigint (主鍵)
- UserID: int (用戶ID)
- Content: nvarchar(200) (日誌內容)
- ObjID: bigint (對象ID)
- Url: varchar(200) (相關URL)
- TypeID: tinyint (日誌類型)
- Language: tinyint (語言)
- CreateTime: datetime (創建時間)
```

### 4.2 實體關係
- T_USER 與 T_DEPARTMENT: 多對一關係
- T_USER 與 T_APPLY_2: 一對多關係
- T_USER 與 T_APPLY_2B: 一對多關係
- T_USER 與 T_LOG: 一對多關係
- T_JOB 與 T_APPLY_2: 一對多關係
- T_JOB 與 T_APPLY_2B: 一對多關係

## 5. 系統架構

### 5.1 技術架構
- **表示層**: ASP.NET MVC 5 + Razor視圖引擎
- **業務邏輯層**: Handler類（業務處理器）
- **數據訪問層**: Entity Framework 6 + LINQ to SQL
- **數據庫層**: SQL Server
- **前端技術**: HTML5, CSS3, JavaScript, jQuery, Bootstrap

### 5.2 項目結構
```
ECO/
├── CP_NET/                 # 主Web應用程序
│   ├── Areas/             # MVC區域
│   │   └── Manage/        # 管理區域
│   ├── Controllers/       # 控制器
│   │   └── api/          # API控制器
│   ├── Models/           # 視圖模型
│   ├── Views/            # 視圖
│   ├── Scripts/          # JavaScript文件
│   ├── Content/          # CSS樣式文件
│   └── App_Start/        # 應用程序啟動配置
├── Database/             # 數據庫項目
├── Models/               # 業務模型
├── HandlerMethod/        # 業務處理器
├── CommonDLL/            # 公共類庫
└── packages/             # NuGet包
```

### 5.3 核心組件

#### 5.3.1 認證和授權
- **OAuth 2.0**: 令牌基礎認證
- **自定義授權**: MoaAuthorizeAttribute
- **Windows認證**: 域用戶集成
- **會話管理**: 基於Session的用戶狀態

#### 5.3.2 數據訪問
- **ORM框架**: Entity Framework 6
- **數據上下文**: CP_LinqDataContext
- **連接字符串**: 配置化數據庫連接
- **事務管理**: 自動事務處理

#### 5.3.3 文件處理
- **PDF生成**: iTextSharp庫
- **文件上傳**: 支持多種文件格式
- **郵件發送**: Mafly.Mail組件
- **圖片處理**: 支持圖片壓縮和處理

## 6. API接口規範

### 6.1 用戶管理API

#### 6.1.1 用戶刪除
- **URL**: `/api/UserApi/DelUser`
- **方法**: DELETE
- **權限**: 管理員
- **參數**: `{ ID: int }`
- **返回**: `{ IsSuccess: bool, Message: string }`

#### 6.1.2 用戶信息獲取
- **URL**: `/api/UserApi/GetUser/{id}`
- **方法**: GET
- **權限**: 已認證用戶
- **參數**: `id: int`
- **返回**: `UserDto對象`

### 6.2 申請管理API

#### 6.2.1 申請2B刪除
- **URL**: `/api/Apply2BApi/Del`
- **方法**: DELETE
- **權限**: 管理員
- **參數**: `{ ID: long }`
- **返回**: `{ IsSuccess: bool, Message: string }`

#### 6.2.2 郵件發送
- **URL**: `/api/Apply2BApi/SendMail`
- **方法**: POST
- **權限**: 已認證用戶
- **參數**:
```json
{
  "title": "string",
  "content": "string",
  "attach": "string",
  "email": "string"
}
```
- **返回**: `{ IsSuccess: bool, Message: string }`

### 6.3 部門管理API

#### 6.3.1 部門刪除
- **URL**: `/api/DepartmentApi/Delete`
- **方法**: DELETE
- **權限**: 管理員
- **參數**: `{ IDs: "1,2,3" }`
- **返回**: `{ IsSuccess: bool, Message: string }`

### 6.4 文件上傳API

#### 6.4.1 移動端圖片上傳
- **URL**: `/api/HomeApi/MobileUploadImage`
- **方法**: POST
- **權限**: 已認證用戶
- **參數**: 文件數據
- **返回**: `ApiResultModel`

#### 6.4.2 Web端圖片上傳
- **URL**: `/api/HomeApi/WebUploadImage`
- **方法**: POST
- **權限**: 已認證用戶
- **參數**: 文件數據
- **返回**: `ResultModel`

## 7. 用戶角色和權限矩陣

### 7.1 用戶角色定義

#### 7.1.1 管理員 (ENUserRole.管理员)
- **權限範圍**: 系統完整訪問權限
- **主要功能**:
  - 用戶管理：創建、編輯、刪除用戶
  - 部門管理：管理組織架構
  - 系統配置：屬性管理、系統設置
  - 數據管理：查看所有數據、生成報表
  - 日誌查看：查看系統操作日誌

#### 7.1.2 普通用戶 (ENUserRole.普通用户)
- **權限範圍**: 受限訪問權限
- **主要功能**:
  - 申請管理：創建和編輯申請
  - 個人信息：修改個人資料
  - 查看功能：查看自己的申請和報表

### 7.2 權限級別定義

#### 7.2.1 沒有修改權限 (ENUserAuthority.没有修改权限)
- 只能查看數據，無法進行任何修改操作

#### 7.2.2 修改自己的表格 (ENUserAuthority.修改自己的表格)
- 可以創建和修改自己提交的申請表格
- 無法修改其他用戶的數據

#### 7.2.3 修改全部表格 (ENUserAuthority.修改全部表格)
- 可以修改所有用戶的申請表格
- 具有數據管理權限

### 7.3 權限矩陣

| 功能模塊 | 管理員 | 普通用戶(全部權限) | 普通用戶(自己權限) | 普通用戶(無權限) |
|---------|--------|------------------|------------------|-----------------|
| 用戶管理 | ✓ | ✗ | ✗ | ✗ |
| 部門管理 | ✓ | ✗ | ✗ | ✗ |
| 創建申請 | ✓ | ✓ | ✓ | ✗ |
| 修改自己申請 | ✓ | ✓ | ✓ | ✗ |
| 修改他人申請 | ✓ | ✓ | ✗ | ✗ |
| 刪除申請 | ✓ | ✗ | ✗ | ✗ |
| 查看申請 | ✓ | ✓ | ✓(限自己部門) | ✓(限自己部門) |
| 生成報表 | ✓ | ✓ | ✓ | ✗ |
| 系統日誌 | ✓ | ✗ | ✗ | ✗ |

## 8. 業務流程

### 8.1 用戶註冊流程
```mermaid
graph TD
    A[用戶提交註冊申請] --> B[填寫基本信息]
    B --> C[系統驗證信息]
    C --> D{驗證通過?}
    D -->|是| E[創建用戶賬號]
    D -->|否| F[返回錯誤信息]
    E --> G[分配默認權限]
    G --> H[發送確認郵件]
    H --> I[註冊完成]
    F --> B
```

### 8.2 工傷申請流程
```mermaid
graph TD
    A[用戶登錄系統] --> B[選擇申請類型]
    B --> C{申請類型}
    C -->|表格2| D[填寫事故報告]
    C -->|表格2B| E[填寫補償申請]
    D --> F[提交申請]
    E --> F
    F --> G[系統驗證]
    G --> H{驗證通過?}
    H -->|是| I[生成PDF文檔]
    H -->|否| J[返回修改]
    I --> K[發送郵件通知]
    K --> L[申請完成]
    J --> D
    J --> E
```

### 8.3 報表生成流程
```mermaid
graph TD
    A[用戶選擇報表類型] --> B[設置報表參數]
    B --> C[查詢數據]
    C --> D[數據處理和統計]
    D --> E[生成報表內容]
    E --> F[創建PDF文檔]
    F --> G[保存報表記錄]
    G --> H[提供下載鏈接]
    H --> I[報表生成完成]
```

## 9. 數據字典

### 9.1 枚舉類型定義

#### 9.1.1 用戶角色 (ENUserRole)
- `管理员 = 1`: 系統管理員
- `普通用户 = 2`: 普通用戶

#### 9.1.2 用戶權限 (ENUserAuthority)
- `没有修改权限 = 1`: 只讀權限
- `修改自己的表格 = 2`: 修改自己數據權限
- `修改全部表格 = 3`: 修改所有數據權限

#### 9.1.3 語言設置 (ENLanguage)
- `中文 = 1`: 繁體中文
- `英文 = 2`: 英語

#### 9.1.4 日誌類型 (ENLogType)
- `表格2编辑 = 1`: 表格2編輯操作
- `表格2B编辑 = 2`: 表格2B編輯操作
- `用户登录 = 3`: 用戶登錄操作
- `系统操作 = 4`: 其他系統操作

#### 9.1.5 身份證類型 (ENEmployeeIDType)
- `香港身份证 = 1`: 香港身份證
- `护照 = 2`: 護照
- `其他 = 3`: 其他證件

#### 9.1.6 事故結果 (ENAccidentResult)
- `轻伤 = 1`: 輕傷
- `重伤 = 2`: 重傷
- `死亡 = 3`: 死亡

#### 9.1.7 屬性類型 (ENAttributeType)
- `工程性质 = 1`: 工程性質分類
- `公营工程 = 2`: 公營工程分類
- `坠下地点细节 = 3`: 墜下地點細節
- `种族 = 4`: 種族分類

#### 9.1.8 語言能力 (ENLanguageAbility)
- `流利 = 1`: 流利
- `一般 = 2`: 一般
- `不懂 = 3`: 不懂

### 9.2 部門編碼規則

#### 9.2.1 管理部門
- `安环部 = 11`: 安全環保部
- `保險公司 = 110`: 保險公司

#### 9.2.2 工程項目部門
部門編碼採用項目代碼形式，如：
- `BEO科技大學 = 34`
- `BGZ合和中心二期地盤平整及地基工程 = 35`
- `BHN青山公路48區TMTL423住宅發展項目 = 36`

### 9.3 數據驗證規則

#### 9.3.1 用戶數據驗證
- **賬號**: 不能包含中文字符，唯一性驗證
- **密碼**: 必填，MD5加密存儲
- **手機號**: 11位數字格式驗證
- **郵箱**: 標準郵箱格式驗證

#### 9.3.2 申請數據驗證
- **申請人姓名**: 必填，最大50字符
- **事故發生時間**: 必填，日期格式
- **身份證號**: 根據類型進行格式驗證
- **保險號碼**: 必填，字符串格式

## 10. 系統配置

### 10.1 數據庫配置
- **服務器**: cob-server-090
- **數據庫**: ECO
- **用戶**: common
- **連接超時**: 400秒
- **多活動結果集**: 啟用

### 10.2 郵件配置
- **郵件組件**: Mafly.Mail
- **發送方式**: SMTP
- **收件人配置**: 通過配置文件設置

### 10.3 文件存儲配置
- **PDF存儲路徑**: ~/PublicFiles/pdf/
- **字體文件路徑**: ~/fonts/
- **上傳文件限制**: 支持常見文件格式

### 10.4 安全配置
- **令牌有效期**: 14天
- **會話超時**: 配置化設置
- **HTTPS**: 生產環境強制使用
- **跨域請求**: 受限制的CORS設置

## 11. 部署和維護

### 11.1 系統要求
- **操作系統**: Windows Server 2012+
- **Web服務器**: IIS 8.0+
- **.NET Framework**: 4.5+
- **數據庫**: SQL Server 2012+

### 11.2 部署步驟
1. 配置IIS應用程序池
2. 部署Web應用程序文件
3. 配置數據庫連接字符串
4. 設置文件夾權限
5. 配置SSL證書
6. 測試系統功能

### 11.3 維護建議
- **定期備份**: 每日數據庫備份
- **日誌監控**: 定期檢查系統日誌
- **性能監控**: 監控系統性能指標
- **安全更新**: 及時應用安全補丁

---

**文檔結束**

*本文檔基於ECO保險系統代碼庫分析生成，版本1.0，創建於2025年7月8日*
