# ECO Insurance System Requirements Document

## Version Information
- **Document Version**: 1.0
- **Creation Date**: July 8, 2025
- **System Name**: ECO Insurance System (Employee Compensation Online System)
- **Development Framework**: ASP.NET MVC 5 + Entity Framework 6
- **Database**: SQL Server

## 1. System Overview

### 1.1 Business Background
The ECO Insurance System is an employee compensation management system specifically designed for Hong Kong's construction industry. The system primarily serves construction companies, insurance companies, and relevant regulatory agencies for processing work injury accident reports, compensation applications, and related document management.

### 1.2 System Objectives
- Digitize work injury accident reporting processes
- Standardize compensation application processing
- Provide multi-language support (Chinese/English)
- Ensure data security and compliance
- Improve processing efficiency and accuracy

### 1.3 System Scope
- User management and access control
- Work injury application form management (Form 2 and Form 2B)
- Work injury report generation and management
- Department and position management
- Document generation and email notifications
- Operation logs and audit trails

## 2. Functional Requirements

### 2.1 User Management Module

#### 2.1.1 User Registration and Authentication
- **Function Description**: Support user registration, login, and password management
- **Main Features**:
  - User registration (requires admin approval)
  - Windows domain authentication integration
  - Encrypted password storage (MD5)
  - Password change functionality
  - Session management

#### 2.1.2 User Permission Management
- **Role Types**:
  - Administrator: Full system access permissions
  - Regular User: Limited access permissions
- **Permission Levels**:
  - No modification rights
  - Modify own forms
  - Modify all forms

#### 2.1.3 User Information Management
- **User Attributes**:
  - Basic Information: Account, Name, English Name, Mobile, Email
  - Organization Information: Department ID (supports multi-level departments)
  - Permission Information: Role, Permission Level
  - Audit Information: Creation Time, Last Login Time

### 2.2 Application Management Module

#### 2.2.1 Form 2 (Work Injury Accident Report)
- **Function Description**: Process initial reports of work injury accidents
- **Main Fields**:
  - Applicant Information: Name, Position, Department
  - Accident Details: Time, Location, Cause, Result
  - Injured Person Information: ID Type, Number, Contact Information
  - Insurance Details: Insurance Number, Insurance Company Information
  - Income Details: Working Days, Other Income

#### 2.2.2 Form 2B (Work Injury Compensation Application)
- **Function Description**: Process formal applications for work injury compensation
- **Main Fields**:
  - Basic Information: Applicant, Injured Employee Information
  - Accident Information: Accident Type, Location, Injured Body Parts
  - Medical Information: Hospital Name, Treatment Details
  - Compensation Information: Application Amount, Payment Status
  - Language Settings: Support Chinese and English bilingual

#### 2.2.3 Application Process Management
- **Status Tracking**: Draft, Submitted, Under Review, Approved, Rejected
- **Approval Process**: Multi-level approval mechanism
- **Notification Mechanism**: Email notifications to relevant personnel
- **Document Generation**: Automatic PDF document generation

### 2.3 Report Management Module

#### 2.3.1 Work Injury Report Generation
- **Function Description**: Generate various work injury statistical reports
- **Report Types**:
  - Monthly work injury statistical reports
  - Department work injury analysis reports
  - Accident type statistical reports
  - Compensation amount statistical reports

#### 2.3.2 Report Management Functions
- **Version Control**: Support report version management
- **File Attachments**: Support attachment upload and management
- **Export Function**: PDF format export
- **Access Control**: Department-based access control

### 2.4 Department Management Module

#### 2.4.1 Department Structure Management
- **Function Description**: Manage organizational structure and department information
- **Department Types**: Support multi-level department structure
- **Department Information**: Department Name, Language Settings, Parent Department
- **Department List**: Contains numerous construction project departments

#### 2.4.2 Position Management
- **Function Description**: Manage position information and types
- **Position Attributes**: Position Name, Type, Language Settings
- **Position Classification**: Classified by engineering type

### 2.5 System Administration Module

#### 2.5.1 Attribute Management
- **Function Description**: Manage various attributes and options in the system
- **Attribute Types**:
  - Engineering Nature
  - Public Works
  - Fall Location Details
  - Race Classification
  - Language Proficiency Levels

#### 2.5.2 Log Management
- **Function Description**: Record and query system operation logs
- **Log Types**:
  - Form 2 Editing
  - Form 2B Editing
  - User Login
  - System Operations
- **Log Content**: Operator, Operation Time, Operation Content, Related Objects

## 3. Non-Functional Requirements

### 3.1 Performance Requirements
- **Response Time**: Page loading time not exceeding 3 seconds
- **Concurrent Users**: Support 100 concurrent users
- **Database Connection**: Connection timeout of 400 seconds
- **File Processing**: Support large file uploads and PDF generation

### 3.2 Security Requirements
- **Authentication**: Windows domain authentication + form authentication
- **Password Security**: MD5 encrypted storage
- **Session Management**: 14-day token validity period
- **Access Control**: Role and permission-based access control
- **Data Protection**: Encrypted storage of sensitive data

### 3.3 Usability Requirements
- **System Availability**: 99.5% or higher
- **Multi-language Support**: Chinese (Traditional) and English
- **Browser Compatibility**: Support mainstream browsers
- **Mobile Devices**: Responsive design support

### 3.4 Maintainability Requirements
- **Code Structure**: Layered architecture design
- **Logging**: Complete operation logs
- **Error Handling**: Unified exception handling mechanism
- **Documentation**: Complete technical documentation

## 4. Data Model

### 4.1 Core Entities

#### 4.1.1 User Entity (T_USER)
```
- ID: int (Primary Key)
- Account: nvarchar(20) (Account)
- Password: varchar(100) (Password)
- Name: nvarchar(100) (Name)
- EnglishName: varchar(100) (English Name)
- Mobile: varchar(100) (Mobile Number)
- Email: varchar(100) (Email)
- DepartMentID: int (Department ID)
- DepartMentID1: int (Department ID1)
- DepartMentID2: int (Department ID2)
- DepartMentID3: int (Department ID3)
- Role: tinyint (Role)
- Authortity: tinyint (Authority)
- CreateTime: datetime (Creation Time)
```

#### 4.1.2 Application Entity (T_APPLY_2)
```
- ID: bigint (Primary Key)
- JobID: int (Job ID)
- UserID: int (User ID)
- ApplyerName: nvarchar(50) (Applicant Name)
- EmployeeName: nvarchar(50) (Employee Name)
- EmployeeIDType: tinyint (ID Type)
- EmployeeID: varchar(50) (ID Number)
- HappenedDate: datetime (Accident Date)
- AccidentResult: tinyint (Accident Result)
- HappenedAddress: nvarchar(200) (Accident Location)
- HospitalName: nvarchar(100) (Hospital Name)
- InsuranceDetail: nvarchar(500) (Insurance Details)
- InsuranceNumber: varchar(50) (Insurance Number)
- CreateTime: datetime (Creation Time)
- Language: tinyint (Language)
```

#### 4.1.3 Application Entity 2B (T_APPLY_2B)
```
- ID: bigint (Primary Key)
- JobID: int (Job ID)
- UserID: int (User ID)
- ApplyerName: nvarchar(50) (Applicant Name)
- EmployeeName: nvarchar(50) (Employee Name)
- EmployeeIDType: tinyint (ID Type)
- EmployeeID: varchar(50) (ID Number)
- HappenedDate: datetime (Accident Date)
- AccidentResult: tinyint (Accident Result)
- HappenedAddress: nvarchar(200) (Accident Location)
- HospitalName: nvarchar(100) (Hospital Name)
- CompensationAmount: decimal (Compensation Amount)
- HasPaid: bit (Has Been Paid)
- PayDate: datetime (Payment Date)
- CreateTime: datetime (Creation Time)
- Language: tinyint (Language)
```

#### 4.1.4 Department Entity (T_DEPARTMENT)
```
- ID: int (Primary Key)
- Name: nvarchar(100) (Department Name)
- Language: tinyint (Language)
- ParentID: int (Parent Department ID)
- CreateTime: datetime (Creation Time)
```

#### 4.1.5 Job Entity (T_JOB)
```
- ID: int (Primary Key)
- Name: nvarchar(100) (Job Title)
- Type: tinyint (Job Type)
- Language: tinyint (Language)
- CreateTime: datetime (Creation Time)
```

#### 4.1.6 Report Entity (T_Report_O)
```
- ID: bigint (Primary Key)
- ApplyName: nvarchar(50) (Applicant Name)
- DeptID: int (Department ID)
- VersionID: bigint (Version ID)
- VersionControl: varchar(50) (Version Control)
- AttachUrl: nvarchar(500) (Attachment URL)
- CreateTime: datetime (Creation Time)
- Language: tinyint (Language)
```

#### 4.1.7 Attribute Entity (T_ATTRIBUTE)
```
- ID: int (Primary Key)
- Name: nvarchar(100) (Attribute Name)
- GroupName: nvarchar(20) (Group Name)
- Type: tinyint (Attribute Type)
- Language: tinyint (Language)
- SpecialIndex: varchar(3) (Special Index)
- CreateTime: datetime (Creation Time)
```

#### 4.1.8 Log Entity (T_LOG)
```
- ID: bigint (Primary Key)
- UserID: int (User ID)
- Content: nvarchar(200) (Log Content)
- ObjID: bigint (Object ID)
- Url: varchar(200) (Related URL)
- TypeID: tinyint (Log Type)
- Language: tinyint (Language)
- CreateTime: datetime (Creation Time)
```

### 4.2 Entity Relationships
- T_USER to T_DEPARTMENT: Many-to-One relationship
- T_USER to T_APPLY_2: One-to-Many relationship
- T_USER to T_APPLY_2B: One-to-Many relationship
- T_USER to T_LOG: One-to-Many relationship
- T_JOB to T_APPLY_2: One-to-Many relationship
- T_JOB to T_APPLY_2B: One-to-Many relationship

## 5. System Architecture

### 5.1 Technical Architecture
- **Presentation Layer**: ASP.NET MVC 5 + Razor View Engine
- **Business Logic Layer**: Handler Classes (Business Processors)
- **Data Access Layer**: Entity Framework 6 + LINQ to SQL
- **Database Layer**: SQL Server
- **Frontend Technologies**: HTML5, CSS3, JavaScript, jQuery, Bootstrap

### 5.2 Project Structure
```
ECO/
├── CP_NET/                 # Main Web Application
│   ├── Areas/             # MVC Areas
│   │   └── Manage/        # Management Area
│   ├── Controllers/       # Controllers
│   │   └── api/          # API Controllers
│   ├── Models/           # View Models
│   ├── Views/            # Views
│   ├── Scripts/          # JavaScript Files
│   ├── Content/          # CSS Style Files
│   └── App_Start/        # Application Startup Configuration
├── Database/             # Database Project
├── Models/               # Business Models
├── HandlerMethod/        # Business Handlers
├── CommonDLL/            # Common Class Library
└── packages/             # NuGet Packages
```

### 5.3 Core Components

#### 5.3.1 Authentication and Authorization
- **OAuth 2.0**: Token-based authentication
- **Custom Authorization**: MoaAuthorizeAttribute
- **Windows Authentication**: Domain user integration
- **Session Management**: Session-based user state

#### 5.3.2 Data Access
- **ORM Framework**: Entity Framework 6
- **Data Context**: CP_LinqDataContext
- **Connection String**: Configurable database connection
- **Transaction Management**: Automatic transaction handling

#### 5.3.3 File Processing
- **PDF Generation**: iTextSharp library
- **File Upload**: Support multiple file formats
- **Email Sending**: Mafly.Mail component
- **Image Processing**: Support image compression and processing

## 6. API Interface Specifications

### 6.1 User Management API

#### 6.1.1 User Deletion
- **URL**: `/api/UserApi/DelUser`
- **Method**: DELETE
- **Permission**: Administrator
- **Parameters**: `{ ID: int }`
- **Response**: `{ IsSuccess: bool, Message: string }`

#### 6.1.2 Get User Information
- **URL**: `/api/UserApi/GetUser/{id}`
- **Method**: GET
- **Permission**: Authenticated User
- **Parameters**: `id: int`
- **Response**: `UserDto Object`

### 6.2 Application Management API

#### 6.2.1 Application 2B Deletion
- **URL**: `/api/Apply2BApi/Del`
- **Method**: DELETE
- **Permission**: Administrator
- **Parameters**: `{ ID: long }`
- **Response**: `{ IsSuccess: bool, Message: string }`

#### 6.2.2 Send Email
- **URL**: `/api/Apply2BApi/SendMail`
- **Method**: POST
- **Permission**: Authenticated User
- **Parameters**:
```json
{
  "title": "string",
  "content": "string",
  "attach": "string",
  "email": "string"
}
```
- **Response**: `{ IsSuccess: bool, Message: string }`

### 6.3 Department Management API

#### 6.3.1 Department Deletion
- **URL**: `/api/DepartmentApi/Delete`
- **Method**: DELETE
- **Permission**: Administrator
- **Parameters**: `{ IDs: "1,2,3" }`
- **Response**: `{ IsSuccess: bool, Message: string }`

### 6.4 File Upload API

#### 6.4.1 Mobile Image Upload
- **URL**: `/api/HomeApi/MobileUploadImage`
- **Method**: POST
- **Permission**: Authenticated User
- **Parameters**: File Data
- **Response**: `ApiResultModel`

#### 6.4.2 Web Image Upload
- **URL**: `/api/HomeApi/WebUploadImage`
- **Method**: POST
- **Permission**: Authenticated User
- **Parameters**: File Data
- **Response**: `ResultModel`

## 7. User Roles and Permission Matrix

### 7.1 User Role Definitions

#### 7.1.1 Administrator (ENUserRole.管理员)
- **Permission Scope**: Full system access permissions
- **Main Functions**:
  - User Management: Create, edit, delete users
  - Department Management: Manage organizational structure
  - System Configuration: Attribute management, system settings
  - Data Management: View all data, generate reports
  - Log Viewing: View system operation logs

#### 7.1.2 Regular User (ENUserRole.普通用户)
- **Permission Scope**: Limited access permissions
- **Main Functions**:
  - Application Management: Create and edit applications
  - Personal Information: Modify personal profile
  - View Functions: View own applications and reports

### 7.2 Permission Level Definitions

#### 7.2.1 No Modification Rights (ENUserAuthority.没有修改权限)
- Can only view data, cannot perform any modification operations

#### 7.2.2 Modify Own Forms (ENUserAuthority.修改自己的表格)
- Can create and modify own submitted application forms
- Cannot modify other users' data

#### 7.2.3 Modify All Forms (ENUserAuthority.修改全部表格)
- Can modify all users' application forms
- Has data management permissions

### 7.3 Permission Matrix

| Function Module | Administrator | Regular User (All Rights) | Regular User (Own Rights) | Regular User (No Rights) |
|----------------|---------------|---------------------------|--------------------------|-------------------------|
| User Management | ✓ | ✗ | ✗ | ✗ |
| Department Management | ✓ | ✗ | ✗ | ✗ |
| Create Application | ✓ | ✓ | ✓ | ✗ |
| Modify Own Application | ✓ | ✓ | ✓ | ✗ |
| Modify Others' Application | ✓ | ✓ | ✗ | ✗ |
| Delete Application | ✓ | ✗ | ✗ | ✗ |
| View Application | ✓ | ✓ | ✓(Own Dept) | ✓(Own Dept) |
| Generate Reports | ✓ | ✓ | ✓ | ✗ |
| System Logs | ✓ | ✗ | ✗ | ✗ |

## 8. Business Processes

### 8.1 User Registration Process
```mermaid
graph TD
    A[User Submits Registration] --> B[Fill Basic Information]
    B --> C[System Validates Information]
    C --> D{Validation Passed?}
    D -->|Yes| E[Create User Account]
    D -->|No| F[Return Error Message]
    E --> G[Assign Default Permissions]
    G --> H[Send Confirmation Email]
    H --> I[Registration Complete]
    F --> B
```

### 8.2 Work Injury Application Process
```mermaid
graph TD
    A[User Logs into System] --> B[Select Application Type]
    B --> C{Application Type}
    C -->|Form 2| D[Fill Accident Report]
    C -->|Form 2B| E[Fill Compensation Application]
    D --> F[Submit Application]
    E --> F
    F --> G[System Validation]
    G --> H{Validation Passed?}
    H -->|Yes| I[Generate PDF Document]
    H -->|No| J[Return for Modification]
    I --> K[Send Email Notification]
    K --> L[Application Complete]
    J --> D
    J --> E
```

### 8.3 Report Generation Process
```mermaid
graph TD
    A[User Selects Report Type] --> B[Set Report Parameters]
    B --> C[Query Data]
    C --> D[Data Processing and Statistics]
    D --> E[Generate Report Content]
    E --> F[Create PDF Document]
    F --> G[Save Report Record]
    G --> H[Provide Download Link]
    H --> I[Report Generation Complete]
```

## 9. Data Dictionary

### 9.1 Enumeration Type Definitions

#### 9.1.1 User Role (ENUserRole)
- `管理员 = 1`: System Administrator
- `普通用户 = 2`: Regular User

#### 9.1.2 User Authority (ENUserAuthority)
- `没有修改权限 = 1`: Read-only permission
- `修改自己的表格 = 2`: Modify own data permission
- `修改全部表格 = 3`: Modify all data permission

#### 9.1.3 Language Setting (ENLanguage)
- `中文 = 1`: Traditional Chinese
- `英文 = 2`: English

#### 9.1.4 Log Type (ENLogType)
- `表格2编辑 = 1`: Form 2 editing operation
- `表格2B编辑 = 2`: Form 2B editing operation
- `用户登录 = 3`: User login operation
- `系统操作 = 4`: Other system operations

#### 9.1.5 Employee ID Type (ENEmployeeIDType)
- `香港身份证 = 1`: Hong Kong Identity Card
- `护照 = 2`: Passport
- `其他 = 3`: Other Documents

#### 9.1.6 Accident Result (ENAccidentResult)
- `轻伤 = 1`: Minor Injury
- `重伤 = 2`: Serious Injury
- `死亡 = 3`: Death

#### 9.1.7 Attribute Type (ENAttributeType)
- `工程性质 = 1`: Engineering Nature Classification
- `公营工程 = 2`: Public Works Classification
- `坠下地点细节 = 3`: Fall Location Details
- `种族 = 4`: Race Classification

#### 9.1.8 Language Ability (ENLanguageAbility)
- `流利 = 1`: Fluent
- `一般 = 2`: Average
- `不懂 = 3`: Not Understood

### 9.2 Department Coding Rules

#### 9.2.1 Administrative Departments
- `安环部 = 11`: Safety and Environmental Department
- `保險公司 = 110`: Insurance Company

#### 9.2.2 Engineering Project Departments
Department codes use project code format, such as:
- `BEO科技大學 = 34`
- `BGZ合和中心二期地盤平整及地基工程 = 35`
- `BHN青山公路48區TMTL423住宅發展項目 = 36`

### 9.3 Data Validation Rules

#### 9.3.1 User Data Validation
- **Account**: Cannot contain Chinese characters, uniqueness validation
- **Password**: Required, MD5 encrypted storage
- **Mobile**: 11-digit number format validation
- **Email**: Standard email format validation

#### 9.3.2 Application Data Validation
- **Applicant Name**: Required, maximum 50 characters
- **Accident Date**: Required, date format
- **ID Number**: Format validation based on type
- **Insurance Number**: Required, string format

## 10. System Configuration

### 10.1 Database Configuration
- **Server**: cob-server-090
- **Database**: ECO
- **User**: common
- **Connection Timeout**: 400 seconds
- **Multiple Active Result Sets**: Enabled

### 10.2 Email Configuration
- **Email Component**: Mafly.Mail
- **Sending Method**: SMTP
- **Recipient Configuration**: Set through configuration file

### 10.3 File Storage Configuration
- **PDF Storage Path**: ~/PublicFiles/pdf/
- **Font File Path**: ~/fonts/
- **Upload File Restrictions**: Support common file formats

### 10.4 Security Configuration
- **Token Validity**: 14 days
- **Session Timeout**: Configurable setting
- **HTTPS**: Mandatory in production environment
- **Cross-Origin Requests**: Restricted CORS settings

## 11. Deployment and Maintenance

### 11.1 System Requirements
- **Operating System**: Windows Server 2012+
- **Web Server**: IIS 8.0+
- **.NET Framework**: 4.5+
- **Database**: SQL Server 2012+

### 11.2 Deployment Steps
1. Configure IIS application pool
2. Deploy web application files
3. Configure database connection strings
4. Set folder permissions
5. Configure SSL certificates
6. Test system functionality

### 11.3 Maintenance Recommendations
- **Regular Backups**: Daily database backups
- **Log Monitoring**: Regular system log checks
- **Performance Monitoring**: Monitor system performance metrics
- **Security Updates**: Apply security patches promptly

## 12. Technical Specifications

### 12.1 Development Environment
- **IDE**: Visual Studio 2015+
- **Framework**: ASP.NET MVC 5
- **ORM**: Entity Framework 6
- **Package Manager**: NuGet
- **Version Control**: Git (recommended)

### 12.2 Third-Party Libraries
- **PDF Generation**: iTextSharp
- **Email**: Mafly.Mail, MailKit
- **JSON Processing**: Newtonsoft.Json
- **Authentication**: Microsoft.Owin.Security
- **UI Framework**: Bootstrap 3.0
- **JavaScript**: jQuery 1.10.2

### 12.3 Browser Support
- **Internet Explorer**: 9+
- **Chrome**: Latest 2 versions
- **Firefox**: Latest 2 versions
- **Safari**: Latest 2 versions
- **Edge**: Latest version

### 12.4 Mobile Support
- **Responsive Design**: Bootstrap-based responsive layout
- **Touch Support**: Touch-friendly interface elements
- **Mobile Browsers**: iOS Safari, Android Chrome
- **Screen Sizes**: Support from 320px to 1920px

## 13. Security Considerations

### 13.1 Authentication Security
- **Password Policy**: Minimum complexity requirements
- **Account Lockout**: Protection against brute force attacks
- **Session Security**: Secure session token generation
- **Multi-factor Authentication**: Future enhancement consideration

### 13.2 Data Security
- **Encryption**: Sensitive data encryption at rest
- **SQL Injection**: Parameterized queries protection
- **XSS Protection**: Input validation and output encoding
- **CSRF Protection**: Anti-forgery tokens

### 13.3 Network Security
- **HTTPS**: SSL/TLS encryption for data transmission
- **Firewall**: Network-level access control
- **VPN**: Secure remote access options
- **API Security**: Token-based API authentication

## 14. Performance Optimization

### 14.1 Database Optimization
- **Indexing**: Proper database indexing strategy
- **Query Optimization**: Efficient LINQ queries
- **Connection Pooling**: Database connection management
- **Caching**: Application-level caching implementation

### 14.2 Web Application Optimization
- **Bundling**: CSS and JavaScript bundling
- **Minification**: Resource minification
- **Compression**: GZIP compression
- **CDN**: Content delivery network consideration

### 14.3 Monitoring and Logging
- **Application Insights**: Performance monitoring
- **Error Logging**: Comprehensive error tracking
- **Performance Counters**: System performance metrics
- **Health Checks**: Application health monitoring

---

**Document End**

*This document is generated based on ECO Insurance System codebase analysis, Version 1.0, Created on July 8, 2025*
